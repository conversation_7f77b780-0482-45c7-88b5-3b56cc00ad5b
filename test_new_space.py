#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Space الجديد nanami34/ai55
Test New Space nanami34/ai55
"""

import requests
import os
import time
from datetime import datetime

def test_flask_api():
    """اختبار Flask API مباشرة"""
    
    print("🚀 اختبار Flask API للـ Space الجديد")
    print("="*60)
    
    # إعدادات Space الجديد
    SPACE_URL = "https://hf.space/nanami34/ai55"
    API_KEY = "whisper-hf-spaces-2025"
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    
    print(f"🔗 Space URL: {SPACE_URL}")
    print(f"🔑 API Key: {API_KEY}")
    print(f"📁 ملف الفيديو: {VIDEO_FILE}")
    
    # التحقق من وجود الملف
    if not os.path.exists(VIDEO_FILE):
        print(f"❌ خطأ: الملف غير موجود - {VIDEO_FILE}")
        return
    
    file_size = os.path.getsize(VIDEO_FILE) / (1024 * 1024)
    print(f"📊 حجم الملف: {file_size:.2f} MB")
    print()
    
    # اختبار صحة التطبيق أولاً
    try:
        print("🔍 اختبار صحة التطبيق...")
        health_response = requests.get(f"{SPACE_URL}/health", timeout=10)
        
        if health_response.status_code == 200:
            health_data = health_response.json()
            print("✅ التطبيق يعمل بصحة جيدة")
            print(f"   📊 الحالة: {health_data.get('status', 'غير محدد')}")
            print(f"   🤖 النموذج محمل: {health_data.get('model_loaded', 'غير محدد')}")
            print(f"   🔑 API Key: {health_data.get('api_key', 'غير محدد')}")
            print(f"   🏗️ المنصة: {health_data.get('platform', 'غير محدد')}")
        else:
            print(f"⚠️ مشكلة في صحة التطبيق: {health_response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصحة: {e}")
        print("💡 قد يكون التطبيق في وضع النوم، سأحاول التحويل مباشرة...")
    
    print()
    print("📤 بدء تحويل الفيديو...")
    start_time = time.time()
    
    try:
        with open(VIDEO_FILE, 'rb') as video_file:
            files = {'audio': video_file}
            headers = {
                'X-API-Key': API_KEY  # إضافة API Key في الـ headers
            }
            
            response = requests.post(
                f"{SPACE_URL}/api/transcribe",
                files=files,
                headers=headers,
                timeout=300  # 5 دقائق timeout
            )
        
        process_time = time.time() - start_time
        print(f"⏱️ وقت الرفع والمعالجة: {process_time:.2f} ثانية")
        
        # التحقق من الاستجابة
        print(f"📡 HTTP Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ تم استلام الاستجابة بنجاح")
                
                if result.get('success'):
                    data = result['data']
                    
                    print("\n" + "="*60)
                    print("🎉 نتائج التحويل:")
                    print("="*60)
                    print(f"📝 النص المحول:")
                    print(f"   {data['text']}")
                    print(f"\n📊 الإحصائيات:")
                    print(f"   🔤 عدد الأحرف: {data['char_count']}")
                    print(f"   📝 عدد الكلمات: {data['word_count']}")
                    print(f"   🌍 اللغة المكتشفة: {data['language']}")
                    print(f"   🔑 API Key: {data['api_key']}")
                    print(f"   ⏱️ وقت المعالجة: {process_time:.2f} ثانية")
                    
                    # حساب السرعة
                    if file_size > 0:
                        speed = file_size / process_time
                        print(f"   🚀 سرعة المعالجة: {speed:.2f} MB/ثانية")
                    
                    print("="*60)
                    
                    # حفظ النتيجة
                    save_result(data, VIDEO_FILE, process_time, file_size)
                    
                    # عرض مثال API
                    show_api_example(SPACE_URL, API_KEY)
                    
                    return data
                else:
                    print(f"❌ خطأ من API: {result.get('error', 'خطأ غير محدد')}")
                    return None
                    
            except ValueError as e:
                print(f"❌ خطأ في تحليل JSON: {e}")
                print(f"📄 محتوى الاستجابة: {response.text[:500]}...")
                return None
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            print(f"📄 محتوى الخطأ: {response.text[:500]}...")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الانتظار (Timeout)")
        print("💡 الملف قد يكون كبير جداً أو الخادم مشغول")
        return None
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ في الاتصال")
        print("💡 تحقق من رابط Space أو الاتصال بالإنترنت")
        return None
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return None

def save_result(data, video_file, process_time, file_size):
    """حفظ النتيجة في ملف"""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"new_space_result_{timestamp}.txt"
    
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(f"نتائج تحويل Space الجديد nanami34/ai55\n")
        f.write(f"{'='*50}\n")
        f.write(f"الملف: {video_file}\n")
        f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"وقت المعالجة: {process_time:.2f} ثانية\n")
        f.write(f"حجم الملف: {file_size:.2f} MB\n")
        f.write(f"اللغة المكتشفة: {data['language']}\n")
        f.write(f"عدد الكلمات: {data['word_count']}\n")
        f.write(f"عدد الأحرف: {data['char_count']}\n")
        f.write(f"API Key: {data['api_key']}\n")
        f.write(f"\n{'='*50}\n")
        f.write(f"النص المحول:\n")
        f.write(f"{'='*50}\n")
        f.write(data['text'])
    
    print(f"💾 تم حفظ النتيجة في: {output_file}")

def show_api_example(space_url, api_key):
    """عرض مثال لاستخدام API"""
    
    print(f"\n🔧 مثال لاستخدام API:")
    print("="*50)
    print("```python")
    print("import requests")
    print("")
    print("# إعدادات API")
    print(f'SPACE_URL = "{space_url}"')
    print(f'API_KEY = "{api_key}"')
    print("")
    print("# رفع ملف صوتي/فيديو")
    print("files = {'audio': open('your_file.mp3', 'rb')}")
    print("headers = {'X-API-Key': API_KEY}")
    print("response = requests.post(f'{SPACE_URL}/api/transcribe', files=files, headers=headers)")
    print("")
    print("# معالجة النتيجة")
    print("result = response.json()")
    print("if result['success']:")
    print("    data = result['data']")
    print("    print(f'النص: {data[\"text\"]}')") 
    print("    print(f'الكلمات: {data[\"word_count\"]}')") 
    print("    print(f'اللغة: {data[\"language\"]}')") 
    print("else:")
    print("    print(f'خطأ: {result[\"error\"]}')") 
    print("```")

def test_gradio_client():
    """اختبار باستخدام gradio_client كبديل"""
    
    print(f"\n🔄 اختبار باستخدام gradio_client...")
    print("="*40)
    
    try:
        from gradio_client import Client
        from config import HUGGING_FACE_TOKEN, SPACE_NAME
        
        print(f"🏠 Space: {SPACE_NAME}")
        print(f"🔑 Token: {HUGGING_FACE_TOKEN[:10]}...{HUGGING_FACE_TOKEN[-5:]}")
        
        # الاتصال
        client = Client(SPACE_NAME, hf_token=HUGGING_FACE_TOKEN)
        print("✅ تم الاتصال بـ gradio_client")
        
        # تحويل الفيديو
        VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
        result = client.predict(VIDEO_FILE, "Arabic", api_name="/predict")
        
        print("✅ تم التحويل باستخدام gradio_client")
        print(f"📄 النتيجة: {result}")
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في gradio_client: {str(e)}")
        return None

def main():
    """الدالة الرئيسية"""
    
    print("🎬 اختبار Space الجديد nanami34/ai55")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # اختبار Flask API أولاً
    result = test_flask_api()
    
    if result:
        print("\n🎉 تم اختبار Flask API بنجاح!")
    else:
        print("\n⚠️ فشل Flask API، جرب gradio_client...")
        # اختبار gradio_client كبديل
        gradio_result = test_gradio_client()
        
        if gradio_result:
            print("✅ نجح gradio_client")
        else:
            print("❌ فشل في جميع الطرق")
    
    print(f"\n🎯 ملخص:")
    print(f"   🔗 Space URL: https://nanami34-ai55.hf.space")
    print(f"   🔑 API Key: whisper-hf-spaces-2025")
    print(f"   📡 API Endpoint: /api/transcribe")
    print(f"   🏥 Health Check: /health")

if __name__ == "__main__":
    main()
