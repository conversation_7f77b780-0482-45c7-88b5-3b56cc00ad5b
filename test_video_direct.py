#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحويل الفيديو إلى نص مباشرة
Direct video to text conversion test
"""

import whisper
import os
import time
from datetime import datetime
import warnings

# إيقاف التحذيرات
warnings.filterwarnings("ignore")

def test_video_transcription():
    """اختبار تحويل الفيديو إلى نص مباشرة"""
    
    print("🎬 اختبار تحويل الفيديو إلى نص مباشرة")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # إعدادات
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    
    # التحقق من وجود الملف
    if not os.path.exists(VIDEO_FILE):
        print(f"❌ خطأ: الملف غير موجود - {VIDEO_FILE}")
        return
    
    file_size = os.path.getsize(VIDEO_FILE) / (1024 * 1024)
    print(f"📁 ملف الفيديو: {VIDEO_FILE}")
    print(f"📊 حجم الملف: {file_size:.2f} MB")
    print()
    
    try:
        # تحميل النموذج
        print("🔄 تحميل نموذج Whisper...")
        start_load = time.time()
        
        # جرب النموذج المحلي أولاً
        if os.path.exists("./models/small.pt"):
            print("📁 استخدام النموذج المحلي...")
            model = whisper.load_model("./models/small.pt")
            print("✅ تم تحميل النموذج المحلي")
        else:
            print("🌐 تحميل نموذج من الإنترنت...")
            model = whisper.load_model("tiny")  # نموذج صغير للاختبار السريع
            print("✅ تم تحميل نموذج tiny")
        
        load_time = time.time() - start_load
        print(f"⏱️ وقت تحميل النموذج: {load_time:.2f} ثانية")
        print()
        
        # تحويل الفيديو إلى نص
        print("🎯 بدء تحويل الفيديو إلى نص...")
        start_transcribe = time.time()
        
        # تحويل مع اكتشاف اللغة التلقائي
        result = model.transcribe(VIDEO_FILE)
        
        transcribe_time = time.time() - start_transcribe
        total_time = load_time + transcribe_time
        
        # استخراج النتائج
        text = result["text"].strip()
        detected_language = result.get("language", "غير محدد")
        
        # إحصائيات
        word_count = len(text.split()) if text else 0
        char_count = len(text) if text else 0
        
        # عرض النتائج
        print("\n" + "="*60)
        print("🎉 نتائج التحويل:")
        print("="*60)
        print(f"📝 النص المحول:")
        print(f"   {text}")
        print(f"\n📊 الإحصائيات:")
        print(f"   🔤 عدد الأحرف: {char_count}")
        print(f"   📝 عدد الكلمات: {word_count}")
        print(f"   🌍 اللغة المكتشفة: {detected_language}")
        print(f"   ⏱️ وقت التحويل: {transcribe_time:.2f} ثانية")
        print(f"   🕒 الوقت الإجمالي: {total_time:.2f} ثانية")
        print("="*60)
        
        # حفظ النتيجة
        output_file = f"video_transcription_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(f"نتائج تحويل الفيديو إلى نص\n")
            f.write(f"{'='*40}\n")
            f.write(f"الملف: {VIDEO_FILE}\n")
            f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"حجم الملف: {file_size:.2f} MB\n")
            f.write(f"النموذج: {'محلي' if os.path.exists('./models/small.pt') else 'tiny'}\n")
            f.write(f"وقت التحميل: {load_time:.2f} ثانية\n")
            f.write(f"وقت التحويل: {transcribe_time:.2f} ثانية\n")
            f.write(f"الوقت الإجمالي: {total_time:.2f} ثانية\n")
            f.write(f"اللغة المكتشفة: {detected_language}\n")
            f.write(f"عدد الكلمات: {word_count}\n")
            f.write(f"عدد الأحرف: {char_count}\n")
            f.write(f"\n{'='*40}\n")
            f.write(f"النص المحول:\n")
            f.write(f"{'='*40}\n")
            f.write(text)
        
        print(f"💾 تم حفظ النتيجة في: {output_file}")
        
        # تحليل الأداء
        print(f"\n📈 تحليل الأداء:")
        if file_size > 0:
            speed = file_size / transcribe_time
            print(f"   🚀 سرعة المعالجة: {speed:.2f} MB/ثانية")
        
        if word_count > 0:
            words_per_second = word_count / transcribe_time
            print(f"   📝 سرعة الكلمات: {words_per_second:.1f} كلمة/ثانية")
        
        # اختبار API format
        print(f"\n🔗 تنسيق API:")
        api_response = {
            'success': True,
            'data': {
                'text': text,
                'word_count': word_count,
                'char_count': char_count,
                'language': detected_language,
                'processing_time': transcribe_time,
                'file_size_mb': file_size
            }
        }
        
        print(f"   📊 استجابة API:")
        print(f"   {api_response}")
        
        return api_response
        
    except Exception as e:
        print(f"❌ خطأ في التحويل: {str(e)}")
        return None

def show_api_example(result):
    """عرض مثال لاستخدام API"""
    if not result:
        return
        
    print(f"\n🔧 مثال لاستخدام API:")
    print("="*40)
    print("```python")
    print("import requests")
    print("")
    print("# رفع فيديو إلى API")
    print("files = {'audio': open('video.mp4', 'rb')}")
    print("response = requests.post('YOUR_API_URL/api/transcribe', files=files)")
    print("result = response.json()")
    print("")
    print("if result['success']:")
    print("    data = result['data']")
    print(f"    print(f'النص: {{data[\"text\"]}}')")
    print(f"    print(f'الكلمات: {{data[\"word_count\"]}}')")
    print(f"    print(f'اللغة: {{data[\"language\"]}}')")
    print("```")

if __name__ == "__main__":
    result = test_video_transcription()
    show_api_example(result)
    
    print(f"\n🎉 انتهى الاختبار!")
    print(f"💡 يمكنك الآن استخدام نفس هذا الكود في API الخاص بك")
