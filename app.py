#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق Flask خالص - بدون Gradio أو Streamlit
Pure Flask app - without Gradio or Streamlit
"""

from flask import Flask, request, jsonify, render_template_string
import whisper
import os
import tempfile
import warnings
import json

# إيقاف التحذيرات
warnings.filterwarnings("ignore")

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 25 * 1024 * 1024  # 25MB حد أقصى

# متغيرات عامة
model = None
API_KEY = "whisper-pure-flask-2025"

def load_whisper_model():
    """تحميل نموذج Whisper"""
    global model
    if model is None:
        try:
            if os.path.exists("./models/small.pt"):
                model = whisper.load_model("./models/small.pt")
                print("✅ تم تحميل النموذج المحلي")
            else:
                model = whisper.load_model("tiny")
                print("✅ تم تحميل نموذج tiny")
        except Exception as e:
            print(f"خطأ في تحميل النموذج: {e}")
            model = whisper.load_model("base")
            print("✅ تم تحميل نموذج base")
    return model

# HTML للواجهة
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول الصوت إلى نص</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 50px;
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(45deg, #f8f9ff, #e8f4fd);
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #5a6fd8;
            background: linear-gradient(45deg, #e8f4fd, #f8f9ff);
        }
        input[type="file"] {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            width: 100%;
            font-size: 16px;
            background: white;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 18px;
            width: 100%;
            margin: 15px 0;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        #result {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            min-height: 150px;
            white-space: pre-wrap;
            font-size: 16px;
            line-height: 1.6;
            border: 1px solid #e9ecef;
        }
        .api-info {
            background: linear-gradient(45deg, #e8f4fd, #fff3cd);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            font-size: 14px;
            border: 1px solid #bee5eb;
        }
        .loading {
            display: none;
            text-align: center;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }
        .success { color: #28a745; background: #d4edda; border: 1px solid #c3e6cb; }
        .error { color: #dc3545; background: #f8d7da; border: 1px solid #f5c6cb; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-item {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 محول الصوت إلى نص</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            مدعوم بنموذج Whisper - دقة عالية في التحويل
        </p>
        
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-area">
                <h3>📁 ارفع ملف صوتي</h3>
                <input type="file" name="audio" accept="audio/*" required>
                <p style="color: #666; margin-top: 15px;">
                    يدعم: MP3, WAV, M4A, FLAC, OGG<br>
                    حد أقصى: 25MB
                </p>
            </div>
            
            <button type="submit" id="submitBtn">🚀 تحويل إلى نص</button>
        </form>
        
        <div class="loading" id="loading">
            ⏳ جاري تحويل الصوت إلى نص... يرجى الانتظار
        </div>
        
        <div id="result">النتيجة ستظهر هنا بعد رفع الملف الصوتي...</div>
        
        <div class="api-info">
            <h4>🔑 معلومات API للاستخدام البرمجي:</h4>
            <p><strong>API Key:</strong> <code>{{ api_key }}</code></p>
            <p><strong>Endpoint:</strong> <code>POST /api/transcribe</code></p>
            <pre style="background: #333; color: #fff; padding: 10px; border-radius: 5px; overflow-x: auto; margin-top: 10px;">
import requests

files = {'audio': open('audio.mp3', 'rb')}
response = requests.post('YOUR_SPACE_URL/api/transcribe', files=files)
result = response.json()

if result['success']:
    print(result['data']['text'])
    print(f"عدد الكلمات: {result['data']['word_count']}")
else:
    print(f"خطأ: {result['error']}")
            </pre>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');
            const submitBtn = document.getElementById('submitBtn');
            
            // إظهار حالة التحميل
            loadingDiv.style.display = 'block';
            submitBtn.disabled = true;
            submitBtn.textContent = '⏳ جاري التحويل...';
            resultDiv.textContent = '';
            resultDiv.className = '';
            
            try {
                const response = await fetch('/api/transcribe', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    
                    // عرض النص
                    resultDiv.innerHTML = `
                        <h4>📝 النص المحول:</h4>
                        <p style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0;">${data.text}</p>
                        
                        <div class="stats">
                            <div class="stat-item">
                                <div class="stat-value">${data.char_count}</div>
                                <div class="stat-label">🔤 عدد الأحرف</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${data.word_count}</div>
                                <div class="stat-label">📝 عدد الكلمات</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${data.language}</div>
                                <div class="stat-label">🌍 اللغة المكتشفة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">✅</div>
                                <div class="stat-label">حالة التحويل</div>
                            </div>
                        </div>
                    `;
                    resultDiv.className = 'success';
                } else {
                    resultDiv.textContent = '❌ خطأ: ' + result.error;
                    resultDiv.className = 'error';
                }
            } catch (error) {
                resultDiv.textContent = '❌ خطأ في الاتصال: ' + error.message;
                resultDiv.className = 'error';
            } finally {
                // إخفاء حالة التحميل
                loadingDiv.style.display = 'none';
                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 تحويل إلى نص';
            }
        });
        
        // تحسين تجربة رفع الملفات
        const fileInput = document.querySelector('input[type="file"]');
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                console.log(`تم اختيار ملف: ${file.name} (${fileSize} MB)`);
                
                if (file.size > 25 * 1024 * 1024) {
                    alert('حجم الملف كبير جداً! الحد الأقصى 25MB');
                    this.value = '';
                }
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template_string(HTML_TEMPLATE, api_key=API_KEY)

@app.route('/api/transcribe', methods=['POST'])
def api_transcribe():
    """API endpoint لتحويل الصوت إلى نص"""
    try:
        # التحقق من وجود الملف
        if 'audio' not in request.files:
            return jsonify({
                'success': False,
                'error': 'لم يتم رفع ملف صوتي'
            })
        
        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({
                'success': False,
                'error': 'لم يتم اختيار ملف'
            })
        
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
            audio_file.save(tmp_file.name)
            
            print(f"🎤 معالجة الملف: {audio_file.filename}")
            
            # تحميل النموذج
            whisper_model = load_whisper_model()
            
            # تحويل الصوت إلى نص
            result = whisper_model.transcribe(tmp_file.name)
            text = result["text"].strip()
            
            # حذف الملف المؤقت
            os.unlink(tmp_file.name)
            
            if not text:
                return jsonify({
                    'success': False,
                    'error': 'لم يتم العثور على نص في الملف الصوتي'
                })
            
            # إعداد البيانات
            word_count = len(text.split())
            char_count = len(text)
            detected_language = result.get("language", "غير محدد")
            
            response_data = {
                'success': True,
                'data': {
                    'text': text,
                    'word_count': word_count,
                    'char_count': char_count,
                    'language': detected_language,
                    'api_key': API_KEY
                }
            }
            
            print(f"✅ تم التحويل بنجاح - {word_count} كلمة")
            return jsonify(response_data)
            
    except Exception as e:
        error_msg = str(e)
        print(f"❌ خطأ في التحويل: {error_msg}")
        return jsonify({
            'success': False,
            'error': f'خطأ في معالجة الملف: {error_msg}'
        })

@app.route('/health')
def health():
    """فحص صحة التطبيق"""
    return jsonify({
        'status': 'healthy',
        'api_key': API_KEY,
        'model_loaded': model is not None
    })

if __name__ == '__main__':
    print("🚀 بدء تطبيق Flask الخالص...")
    print(f"🔑 API Key: {API_KEY}")
    print("✅ التطبيق جاهز للاستخدام")
    
    # فحص النموذج المحلي
    if os.path.exists("./models/small.pt"):
        size = os.path.getsize("./models/small.pt") / (1024 * 1024)
        print(f"📁 النموذج المحلي متوفر ({size:.1f}MB)")
    
    # تشغيل التطبيق
    app.run(host='0.0.0.0', port=7860, debug=False)
