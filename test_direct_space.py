#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر للـ Space
Direct Space Test
"""

import requests
import os
import time
from datetime import datetime

def test_space_directly():
    """اختبار Space مباشرة"""
    
    print("🎯 اختبار Space مباشرة")
    print("="*50)
    
    # من logs التطبيق، أرى أنه يعمل على هذه العناوين:
    # * Running on http://127.0.0.1:7860
    # * Running on http://10.108.112.100:7860
    
    # لكن للوصول الخارجي، نحتاج الرابط الصحيح
    # دعني أجرب روابط مختلفة بناءً على اسم Space
    
    possible_urls = [
        # الروابط المحتملة للـ Space
        "https://nanami34-ai55.hf.space",
        "https://hf.space/nanami34-ai55", 
        "https://nanami34-ai55.huggingface.co",
        "https://spaces.huggingface.co/nanami34/ai55",
        
        # روابط بديلة
        "https://hf.co/spaces/nanami34/ai55",
        "https://huggingface.co/spaces/nanami34/ai55/embed",
    ]
    
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    API_KEY = "whisper-hf-spaces-2025"
    
    if not os.path.exists(VIDEO_FILE):
        print(f"❌ الملف غير موجود: {VIDEO_FILE}")
        return
    
    file_size = os.path.getsize(VIDEO_FILE) / (1024 * 1024)
    print(f"📁 ملف الاختبار: {VIDEO_FILE} ({file_size:.2f} MB)")
    print()
    
    for base_url in possible_urls:
        print(f"🔗 اختبار: {base_url}")
        
        # اختبار الصفحة الرئيسية أولاً
        try:
            response = requests.get(base_url, timeout=10)
            print(f"   📡 الصفحة الرئيسية: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ Space متاح!")
                
                # اختبار API endpoints
                api_endpoints = [
                    "/api/transcribe",
                    "/transcribe", 
                    "/health",
                    "/predict"
                ]
                
                for endpoint in api_endpoints:
                    api_url = f"{base_url}{endpoint}"
                    
                    try:
                        if endpoint == "/health":
                            # GET للـ health
                            health_response = requests.get(api_url, timeout=10)
                            print(f"   🏥 {endpoint}: {health_response.status_code}")
                            
                            if health_response.status_code == 200:
                                try:
                                    health_data = health_response.json()
                                    print(f"      ✅ صحة التطبيق: {health_data.get('status')}")
                                    print(f"      🤖 النموذج: {health_data.get('model_loaded')}")
                                except:
                                    print(f"      📄 استجابة: {health_response.text[:50]}...")
                        else:
                            # POST للـ transcribe
                            print(f"   🎯 اختبار {endpoint}...")
                            
                            with open(VIDEO_FILE, 'rb') as f:
                                files = {'audio': f}
                                headers = {'X-API-Key': API_KEY}
                                
                                start_time = time.time()
                                api_response = requests.post(
                                    api_url, 
                                    files=files, 
                                    headers=headers, 
                                    timeout=120
                                )
                                process_time = time.time() - start_time
                                
                                print(f"      📡 Status: {api_response.status_code}")
                                print(f"      ⏱️ وقت: {process_time:.2f}s")
                                
                                if api_response.status_code == 200:
                                    try:
                                        result = api_response.json()
                                        if result.get('success'):
                                            data = result['data']
                                            print(f"      🎉 نجح التحويل!")
                                            print(f"      📝 النص: {data['text'][:80]}...")
                                            print(f"      📊 كلمات: {data['word_count']}")
                                            print(f"      🌍 لغة: {data['language']}")
                                            
                                            # حفظ النتيجة الناجحة
                                            save_result(data, api_url, process_time)
                                            show_success_info(api_url, API_KEY)
                                            return True
                                        else:
                                            print(f"      ❌ خطأ API: {result.get('error')}")
                                    except:
                                        print(f"      📄 استجابة غير JSON: {api_response.text[:100]}...")
                                else:
                                    print(f"      ❌ فشل: {api_response.text[:100]}...")
                                    
                    except requests.exceptions.Timeout:
                        print(f"   ⏰ {endpoint}: انتهت المهلة")
                    except Exception as e:
                        print(f"   ❌ {endpoint}: {str(e)[:50]}...")
                
            elif response.status_code == 401:
                print(f"   🔐 يحتاج مصادقة")
            elif response.status_code == 404:
                print(f"   ❌ غير موجود")
            else:
                print(f"   ⚠️ حالة غير معروفة: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ: {str(e)[:50]}...")
        
        print()  # سطر فارغ بين الاختبارات
    
    return False

def save_result(data, api_url, process_time):
    """حفظ النتيجة الناجحة"""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"working_api_result_{timestamp}.txt"
    
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(f"🎉 API يعمل بنجاح!\n")
        f.write(f"{'='*40}\n")
        f.write(f"API URL: {api_url}\n")
        f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"وقت المعالجة: {process_time:.2f} ثانية\n")
        f.write(f"عدد الكلمات: {data['word_count']}\n")
        f.write(f"عدد الأحرف: {data['char_count']}\n")
        f.write(f"اللغة: {data['language']}\n")
        f.write(f"API Key: {data['api_key']}\n")
        f.write(f"\n{'='*40}\n")
        f.write(f"النص المحول:\n")
        f.write(f"{'='*40}\n")
        f.write(data['text'])
        f.write(f"\n\n{'='*40}\n")
        f.write(f"مثال للاستخدام:\n")
        f.write(f"{'='*40}\n")
        f.write(f"import requests\n\n")
        f.write(f"files = {{'audio': open('file.mp3', 'rb')}}\n")
        f.write(f"headers = {{'X-API-Key': '{data['api_key']}'}}\n")
        f.write(f"response = requests.post('{api_url}', files=files, headers=headers)\n")
        f.write(f"result = response.json()\n")
        f.write(f"print(result['data']['text'])\n")
    
    print(f"      💾 حفظ في: {output_file}")

def show_success_info(api_url, api_key):
    """عرض معلومات النجاح"""
    
    print(f"\n🎉 تم العثور على API يعمل!")
    print("="*50)
    print(f"🔗 API URL: {api_url}")
    print(f"🔑 API Key: {api_key}")
    print()
    print("💻 مثال للاستخدام:")
    print("```python")
    print("import requests")
    print()
    print("# إعدادات")
    print(f"API_URL = '{api_url}'")
    print(f"API_KEY = '{api_key}'")
    print()
    print("# رفع ملف")
    print("files = {'audio': open('your_file.mp3', 'rb')}")
    print("headers = {'X-API-Key': API_KEY}")
    print("response = requests.post(API_URL, files=files, headers=headers)")
    print()
    print("# معالجة النتيجة")
    print("if response.status_code == 200:")
    print("    result = response.json()")
    print("    if result['success']:")
    print("        data = result['data']")
    print("        print(f'النص: {data[\"text\"]}')") 
    print("        print(f'الكلمات: {data[\"word_count\"]}')") 
    print("        print(f'اللغة: {data[\"language\"]}')") 
    print("    else:")
    print("        print(f'خطأ: {result[\"error\"]}')") 
    print("else:")
    print("    print(f'HTTP Error: {response.status_code}')") 
    print("```")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 اختبار مباشر للعثور على Space يعمل")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    success = test_space_directly()
    
    if not success:
        print("❌ لم يتم العثور على API يعمل")
        print()
        print("💡 اقتراحات:")
        print("   1. تحقق من أن Space يعمل في المتصفح:")
        print("      https://huggingface.co/spaces/nanami34/ai55")
        print("   2. قد يكون Space في وضع النوم - افتحه في المتصفح لتنشيطه")
        print("   3. تحقق من logs Space للأخطاء")
        print("   4. جرب إعادة تشغيل Space")
        print("   5. تأكد من أن التطبيق Flask يعمل بشكل صحيح")

if __name__ == "__main__":
    main()
