#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عميل API للـ Space الخاص
Private Space API Client
"""

import os
import time
from datetime import datetime
from gradio_client import Client

class PrivateSpaceClient:
    """عميل للتعامل مع Space الخاص"""
    
    def __init__(self, hf_token):
        """
        تهيئة العميل
        
        Args:
            hf_token (str): Hugging Face Token
        """
        self.hf_token = hf_token
        self.space_name = "sidivall/ai_extact"
        self.client = None
        
    def connect(self):
        """الاتصال بـ Space"""
        try:
            print("🔗 الاتصال بـ Space الخاص...")
            start_time = time.time()
            
            self.client = Client(self.space_name, hf_token=self.hf_token)
            
            connect_time = time.time() - start_time
            print(f"✅ تم الاتصال بنجاح ({connect_time:.2f} ثانية)")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {str(e)}")
            
            if "401" in str(e) or "unauthorized" in str(e).lower():
                print("🔑 مشكلة في Token - تحقق من:")
                print("   - صحة Token")
                print("   - صلاحيات Token (يجب أن تكون 'Read')")
                print("   - أن Token لم ينته صلاحيته")
            
            return False
    
    def transcribe_file(self, file_path, language="Arabic"):
        """
        تحويل ملف صوتي/فيديو إلى نص
        
        Args:
            file_path (str): مسار الملف
            language (str): اللغة ("Arabic", "English", "تلقائي")
            
        Returns:
            dict: النتيجة مع النص والإحصائيات
        """
        
        if not self.client:
            print("❌ لم يتم الاتصال بـ Space بعد")
            return None
        
        if not os.path.exists(file_path):
            print(f"❌ الملف غير موجود: {file_path}")
            return None
        
        file_size = os.path.getsize(file_path) / (1024 * 1024)
        print(f"📁 الملف: {os.path.basename(file_path)}")
        print(f"📊 الحجم: {file_size:.2f} MB")
        print(f"🌍 اللغة: {language}")
        
        try:
            print("🎯 بدء التحويل...")
            start_time = time.time()
            
            # جرب طرق مختلفة للاستدعاء
            methods = [
                lambda: self.client.predict(file_path, language, api_name="/predict"),
                lambda: self.client.predict(file_path, "Arabic", api_name="/predict"),
                lambda: self.client.predict(file_path, api_name="/predict"),
            ]
            
            result = None
            for i, method in enumerate(methods):
                try:
                    print(f"   🔍 جرب الطريقة {i+1}...")
                    result = method()
                    print(f"   ✅ نجح مع الطريقة {i+1}")
                    break
                except Exception as e:
                    print(f"   ❌ فشل الطريقة {i+1}: {str(e)[:60]}...")
                    continue
            
            if result is None:
                print("❌ فشل في جميع الطرق")
                return None
            
            process_time = time.time() - start_time
            
            # تحليل النتيجة
            response_data = self._parse_result(result, process_time, file_size)
            
            # عرض النتائج
            self._display_results(response_data)
            
            # حفظ النتيجة
            self._save_result(response_data, file_path)
            
            return response_data
            
        except Exception as e:
            print(f"❌ خطأ في التحويل: {str(e)}")
            return None
    
    def _parse_result(self, result, process_time, file_size):
        """تحليل نتيجة API"""
        
        response_data = {
            'success': True,
            'process_time': process_time,
            'file_size_mb': file_size,
            'raw_result': result
        }
        
        if isinstance(result, (list, tuple)) and len(result) > 0:
            # النص عادة في العنصر الأول
            text = str(result[0]) if result[0] else ""
            
            response_data.update({
                'text': text,
                'word_count': len(text.split()) if text else 0,
                'char_count': len(text) if text else 0,
                'language': 'detected',  # قد يكون في عنصر آخر
                'additional_info': result[1:] if len(result) > 1 else []
            })
            
        else:
            # نتيجة مباشرة
            text = str(result)
            response_data.update({
                'text': text,
                'word_count': len(text.split()) if text else 0,
                'char_count': len(text) if text else 0,
                'language': 'unknown'
            })
        
        return response_data
    
    def _display_results(self, data):
        """عرض النتائج"""
        
        print("\n" + "="*60)
        print("🎉 نتائج التحويل:")
        print("="*60)
        print(f"📝 النص المحول:")
        print(f"   {data['text']}")
        print(f"\n📊 الإحصائيات:")
        print(f"   📝 عدد الكلمات: {data['word_count']}")
        print(f"   🔤 عدد الأحرف: {data['char_count']}")
        print(f"   🌍 اللغة: {data['language']}")
        print(f"   ⏱️ وقت المعالجة: {data['process_time']:.2f} ثانية")
        print(f"   📁 حجم الملف: {data['file_size_mb']:.2f} MB")
        
        if data['file_size_mb'] > 0:
            speed = data['file_size_mb'] / data['process_time']
            print(f"   🚀 سرعة المعالجة: {speed:.2f} MB/ثانية")
        
        print("="*60)
    
    def _save_result(self, data, original_file):
        """حفظ النتيجة في ملف"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"private_transcription_{timestamp}.txt"
        
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(f"نتائج تحويل Space الخاص\n")
            f.write(f"{'='*40}\n")
            f.write(f"الملف الأصلي: {original_file}\n")
            f.write(f"Space: {self.space_name}\n")
            f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"وقت المعالجة: {data['process_time']:.2f} ثانية\n")
            f.write(f"حجم الملف: {data['file_size_mb']:.2f} MB\n")
            f.write(f"عدد الكلمات: {data['word_count']}\n")
            f.write(f"عدد الأحرف: {data['char_count']}\n")
            f.write(f"\n{'='*40}\n")
            f.write(f"النص المحول:\n")
            f.write(f"{'='*40}\n")
            f.write(data['text'])
            f.write(f"\n\n{'='*40}\n")
            f.write(f"النتيجة الخام:\n")
            f.write(f"{'='*40}\n")
            f.write(str(data['raw_result']))
        
        print(f"💾 تم حفظ النتيجة في: {output_file}")
    
    def test_connection(self):
        """اختبار الاتصال"""
        
        if not self.client:
            print("❌ لم يتم الاتصال بعد")
            return False
        
        try:
            print("🔍 اختبار الاتصال...")
            # محاولة الحصول على معلومات API
            api_info = self.client.view_api()
            print("✅ الاتصال يعمل بشكل جيد")
            
            if 'named_endpoints' in api_info:
                endpoints = list(api_info['named_endpoints'].keys())
                print(f"📡 Endpoints متاحة: {endpoints}")
            
            return True
            
        except Exception as e:
            print(f"❌ مشكلة في الاتصال: {str(e)}")
            return False

def main():
    """الدالة الرئيسية"""

    print("🔐 عميل API للـ Space الخاص")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)

    # قراءة Token من ملف الإعدادات
    try:
        from config import HUGGING_FACE_TOKEN
        HF_TOKEN = HUGGING_FACE_TOKEN
    except ImportError:
        print("❌ ملف config.py غير موجود")
        HF_TOKEN = "YOUR_HUGGING_FACE_TOKEN_HERE"

    if HF_TOKEN == "YOUR_TOKEN_HERE" or HF_TOKEN == "YOUR_HUGGING_FACE_TOKEN_HERE":
        print("❌ يرجى إضافة Hugging Face Token:")
        print("1. اذهب إلى https://huggingface.co/settings/tokens")
        print("2. أنشئ token جديد مع صلاحية 'Read'")
        print("3. افتح ملف config.py")
        print("4. ضع الـ token في HUGGING_FACE_TOKEN")
        print("5. احفظ الملف وشغل السكريبت مرة أخرى")
        return
    
    # إنشاء العميل
    client = PrivateSpaceClient(HF_TOKEN)
    
    # الاتصال
    if not client.connect():
        print("❌ فشل في الاتصال")
        return
    
    # اختبار الاتصال
    if not client.test_connection():
        print("⚠️ مشكلة في الاتصال")
    
    # تحويل الفيديو
    video_file = "vertical_shorts_exciting_20250715_004149.mp4"
    
    if os.path.exists(video_file):
        print(f"\n🎬 تحويل الفيديو: {video_file}")
        result = client.transcribe_file(video_file, "Arabic")
        
        if result:
            print("\n🎉 تم التحويل بنجاح!")
        else:
            print("\n❌ فشل التحويل")
    else:
        print(f"\n❌ الملف غير موجود: {video_file}")
        print("💡 ضع ملف فيديو/صوتي في نفس المجلد وغير اسم الملف في الكود")

if __name__ == "__main__":
    main()
