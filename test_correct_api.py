#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار API بالرابط الصحيح
Test API with correct URL
"""

import requests
import os
import time
from datetime import datetime

def test_api_with_correct_url():
    """اختبار API بالرابط الصحيح"""
    
    print("🚀 اختبار API بالرابط الصحيح")
    print("="*60)
    
    # الرابط الصحيح الذي وجدناه
    BASE_URL = "https://hf.space/nanami34/ai55"
    API_KEY = "whisper-hf-spaces-2025"
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    
    print(f"🔗 Base URL: {BASE_URL}")
    print(f"🔑 API Key: {API_KEY}")
    print(f"📁 ملف الفيديو: {VIDEO_FILE}")
    
    # التحقق من وجود الملف
    if not os.path.exists(VIDEO_FILE):
        print(f"❌ خطأ: الملف غير موجود - {VIDEO_FILE}")
        return
    
    file_size = os.path.getsize(VIDEO_FILE) / (1024 * 1024)
    print(f"📊 حجم الملف: {file_size:.2f} MB")
    print()
    
    # اختبار endpoints مختلفة
    endpoints_to_test = [
        "/health",
        "/api/transcribe", 
        "/transcribe",
        "/predict",
        "/api/predict"
    ]
    
    print("🔍 اختبار endpoints...")
    working_endpoints = []
    
    for endpoint in endpoints_to_test:
        url = f"{BASE_URL}{endpoint}"
        try:
            if endpoint == "/health":
                # GET request للـ health
                response = requests.get(url, timeout=10)
            else:
                # POST request للـ transcribe endpoints
                with open(VIDEO_FILE, 'rb') as f:
                    files = {'audio': f}
                    headers = {'X-API-Key': API_KEY}
                    response = requests.post(url, files=files, headers=headers, timeout=30)
            
            print(f"   {endpoint}: {response.status_code}")
            
            if response.status_code == 200:
                working_endpoints.append(endpoint)
                print(f"      ✅ يعمل!")
                
                # إذا كان transcribe endpoint، عرض النتيجة
                if 'transcribe' in endpoint or 'predict' in endpoint:
                    try:
                        result = response.json()
                        if result.get('success'):
                            print(f"      🎉 تحويل ناجح!")
                            print(f"      📝 النص: {result['data']['text'][:100]}...")
                            return result
                        else:
                            print(f"      ❌ خطأ: {result.get('error', 'غير محدد')}")
                    except:
                        print(f"      📄 استجابة: {response.text[:100]}...")
                        
            elif response.status_code == 404:
                print(f"      ❌ غير موجود")
            elif response.status_code == 405:
                print(f"      🔄 Method غير مدعوم")
            else:
                print(f"      ⚠️ {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"   {endpoint}: ⏰ Timeout")
        except Exception as e:
            print(f"   {endpoint}: ❌ {str(e)[:50]}...")
    
    if working_endpoints:
        print(f"\n✅ Endpoints تعمل: {working_endpoints}")
    else:
        print(f"\n❌ لا توجد endpoints تعمل")
    
    return None

def test_direct_space_access():
    """اختبار الوصول المباشر للـ Space"""
    
    print(f"\n🎯 اختبار الوصول المباشر للـ Space...")
    print("="*50)
    
    # جرب روابط مختلفة للـ API
    api_urls = [
        "https://hf.space/nanami34/ai55/api/transcribe",
        "https://nanami34-ai55.hf.space/api/transcribe",
        "https://huggingface.co/spaces/nanami34/ai55/api/transcribe",
        "https://hf.space/nanami34/ai55/transcribe",
        "https://hf.space/nanami34/ai55/predict"
    ]
    
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    API_KEY = "whisper-hf-spaces-2025"
    
    for api_url in api_urls:
        print(f"\n🔗 جرب: {api_url}")
        
        try:
            with open(VIDEO_FILE, 'rb') as f:
                files = {'audio': f}
                headers = {'X-API-Key': API_KEY}
                
                start_time = time.time()
                response = requests.post(api_url, files=files, headers=headers, timeout=60)
                process_time = time.time() - start_time
                
                print(f"   📡 Status: {response.status_code}")
                print(f"   ⏱️ وقت: {process_time:.2f}s")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('success'):
                            data = result['data']
                            print(f"   🎉 نجح!")
                            print(f"   📝 النص: {data['text'][:100]}...")
                            print(f"   📊 كلمات: {data['word_count']}")
                            print(f"   🌍 لغة: {data['language']}")
                            
                            # حفظ النتيجة
                            save_successful_result(data, api_url, process_time)
                            return result
                        else:
                            print(f"   ❌ خطأ: {result.get('error')}")
                    except:
                        print(f"   📄 استجابة غير JSON: {response.text[:100]}...")
                else:
                    print(f"   ❌ فشل: {response.text[:100]}...")
                    
        except requests.exceptions.Timeout:
            print(f"   ⏰ انتهت المهلة")
        except Exception as e:
            print(f"   ❌ خطأ: {str(e)[:50]}...")
    
    return None

def save_successful_result(data, api_url, process_time):
    """حفظ النتيجة الناجحة"""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"successful_api_test_{timestamp}.txt"
    
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(f"نتيجة اختبار API ناجح\n")
        f.write(f"{'='*40}\n")
        f.write(f"API URL: {api_url}\n")
        f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"وقت المعالجة: {process_time:.2f} ثانية\n")
        f.write(f"عدد الكلمات: {data['word_count']}\n")
        f.write(f"عدد الأحرف: {data['char_count']}\n")
        f.write(f"اللغة: {data['language']}\n")
        f.write(f"API Key: {data['api_key']}\n")
        f.write(f"\n{'='*40}\n")
        f.write(f"النص المحول:\n")
        f.write(f"{'='*40}\n")
        f.write(data['text'])
    
    print(f"   💾 حفظ في: {output_file}")

def show_final_api_example(working_url):
    """عرض مثال API نهائي"""
    
    print(f"\n🔧 مثال API النهائي:")
    print("="*40)
    print("```python")
    print("import requests")
    print("")
    print(f"# الرابط الذي يعمل")
    print(f"API_URL = '{working_url}'")
    print("API_KEY = 'whisper-hf-spaces-2025'")
    print("")
    print("# رفع ملف")
    print("files = {'audio': open('your_file.mp3', 'rb')}")
    print("headers = {'X-API-Key': API_KEY}")
    print("response = requests.post(API_URL, files=files, headers=headers)")
    print("")
    print("# معالجة النتيجة")
    print("if response.status_code == 200:")
    print("    result = response.json()")
    print("    if result['success']:")
    print("        print(f'النص: {result[\"data\"][\"text\"]}')") 
    print("        print(f'الكلمات: {result[\"data\"][\"word_count\"]}')") 
    print("```")

def main():
    """الدالة الرئيسية"""
    
    print("🎬 اختبار شامل للعثور على API الصحيح")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # اختبار endpoints مختلفة
    result1 = test_api_with_correct_url()
    
    if not result1:
        # اختبار روابط مباشرة
        result2 = test_direct_space_access()
        
        if result2:
            print(f"\n🎉 تم العثور على API يعمل!")
        else:
            print(f"\n❌ لم يتم العثور على API يعمل")
            print(f"\n💡 اقتراحات:")
            print(f"   - تحقق من أن Space يعمل في المتصفح")
            print(f"   - قد يكون Space في وضع النوم")
            print(f"   - جرب إعادة تشغيل Space")
            print(f"   - تحقق من logs Space للأخطاء")
    else:
        print(f"\n🎉 API يعمل بشكل مثالي!")

if __name__ == "__main__":
    main()
