#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Hugging Face Space الخاص
Test Private Hugging Face Space
"""

import os
import time
from datetime import datetime

def test_with_gradio_client():
    """اختبار باستخدام gradio_client"""
    
    print("🔐 اختبار Space الخاص باستخدام gradio_client")
    print("="*60)
    
    try:
        from gradio_client import Client
        print("✅ gradio_client متوفر")
    except ImportError:
        print("❌ gradio_client غير مثبت")
        print("💡 قم بتثبيته: pip install gradio_client")
        return
    
    # إعدادات
    SPACE_NAME = "sidivall/ai_extact"
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    
    # التحقق من وجود الملف
    if not os.path.exists(VIDEO_FILE):
        print(f"❌ خطأ: الملف غير موجود - {VIDEO_FILE}")
        return
    
    file_size = os.path.getsize(VIDEO_FILE) / (1024 * 1024)
    print(f"📁 ملف الفيديو: {VIDEO_FILE}")
    print(f"📊 حجم الملف: {file_size:.2f} MB")
    print(f"🏠 Space: {SPACE_NAME}")
    print()
    
    try:
        print("🔗 الاتصال بـ Space...")
        start_time = time.time()
        
        # محاولة الاتصال بدون token أولاً (قد يعمل إذا كان Space عام)
        client = Client(SPACE_NAME)
        
        connect_time = time.time() - start_time
        print(f"✅ تم الاتصال بنجاح ({connect_time:.2f} ثانية)")
        
        # الحصول على معلومات API
        print("📋 معلومات API:")
        try:
            api_info = client.view_api()
            print(f"   📡 API متاح: نعم")
            print(f"   🔧 Endpoints: {len(api_info.get('named_endpoints', {}))} endpoint")
        except:
            print("   📡 لا يمكن الحصول على معلومات API")
        
        print()
        print("🎯 بدء تحويل الفيديو...")
        transcribe_start = time.time()
        
        # تحويل الفيديو
        # جرب endpoints مختلفة
        possible_endpoints = ["/predict", "/transcribe_audio", "/api/transcribe"]
        
        result = None
        for endpoint in possible_endpoints:
            try:
                print(f"   🔍 جرب endpoint: {endpoint}")
                result = client.predict(
                    VIDEO_FILE,
                    "Arabic",  # اللغة
                    api_name=endpoint
                )
                print(f"   ✅ نجح مع: {endpoint}")
                break
            except Exception as e:
                print(f"   ❌ فشل مع {endpoint}: {str(e)[:100]}...")
                continue
        
        if result is None:
            print("❌ فشل في جميع endpoints")
            return
        
        transcribe_time = time.time() - transcribe_start
        
        # عرض النتائج
        print("\n" + "="*60)
        print("🎉 نتائج التحويل:")
        print("="*60)
        
        if isinstance(result, (list, tuple)):
            if len(result) >= 1:
                text = result[0] if result[0] else "لا يوجد نص"
                print(f"📝 النص المحول:")
                print(f"   {text}")
                
                # إحصائيات
                word_count = len(text.split()) if text else 0
                char_count = len(text) if text else 0
                
                print(f"\n📊 الإحصائيات:")
                print(f"   📝 عدد الكلمات: {word_count}")
                print(f"   🔤 عدد الأحرف: {char_count}")
                print(f"   ⏱️ وقت التحويل: {transcribe_time:.2f} ثانية")
                
                if len(result) >= 2 and result[1]:
                    print(f"   ℹ️ معلومات إضافية: {result[1]}")
                
                if len(result) >= 3 and result[2]:
                    print(f"   🔑 API Info: {result[2]}")
            
        else:
            print(f"📄 النتيجة: {result}")
        
        print("="*60)
        
        # حفظ النتيجة
        output_file = f"private_space_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(f"نتائج تحويل Space الخاص\n")
            f.write(f"{'='*40}\n")
            f.write(f"Space: {SPACE_NAME}\n")
            f.write(f"الملف: {VIDEO_FILE}\n")
            f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"وقت الاتصال: {connect_time:.2f} ثانية\n")
            f.write(f"وقت التحويل: {transcribe_time:.2f} ثانية\n")
            f.write(f"\nالنتيجة:\n{result}")
        
        print(f"💾 تم حفظ النتيجة في: {output_file}")
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ خطأ في الاتصال: {error_msg}")
        
        if "unauthorized" in error_msg.lower() or "private" in error_msg.lower():
            print("\n🔐 Space خاص - تحتاج إلى token:")
            print("1. اذهب إلى https://huggingface.co/settings/tokens")
            print("2. أنشئ token جديد")
            print("3. استخدمه في الكود:")
            print("   client = Client('sidivall/ai_extact', hf_token='YOUR_TOKEN')")
        
        elif "not found" in error_msg.lower():
            print("\n❌ Space غير موجود أو الاسم خاطئ")
            print("🔍 تحقق من الاسم: sidivall/ai_extact")

def test_public_access():
    """اختبار الوصول العام"""
    
    print("\n🌍 اختبار الوصول العام...")
    print("="*40)
    
    import requests
    
    urls_to_test = [
        "https://sidivall-ai-extact.hf.space",
        "https://huggingface.co/spaces/sidivall/ai_extact"
    ]
    
    for url in urls_to_test:
        try:
            print(f"🔍 اختبار: {url}")
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ متاح عام - يمكن استخدام API")
                return url
            elif response.status_code == 401:
                print(f"   🔐 خاص - يحتاج مصادقة")
            elif response.status_code == 404:
                print(f"   ❌ غير موجود")
            else:
                print(f"   ⚠️ حالة غير معروفة: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ خطأ في الاتصال: {str(e)[:50]}...")
    
    return None

def show_solutions():
    """عرض الحلول المتاحة"""
    
    print("\n" + "="*60)
    print("💡 الحلول المتاحة:")
    print("="*60)
    print("1. 🌍 جعل Space عام:")
    print("   - اذهب إلى Settings في Space")
    print("   - غير Visibility من Private إلى Public")
    print("   - احفظ التغييرات")
    print()
    print("2. 🔑 استخدام Token:")
    print("   - أنشئ token من https://huggingface.co/settings/tokens")
    print("   - استخدمه في gradio_client")
    print()
    print("3. 📱 استخدام الواجهة مباشرة:")
    print("   - افتح Space في المتصفح")
    print("   - ارفع الملفات يدوياً")
    print()
    print("أي حل تفضل؟")

if __name__ == "__main__":
    print("🚀 اختبار Hugging Face Space الخاص")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # اختبار الوصول العام أولاً
    public_url = test_public_access()
    
    if not public_url:
        # اختبار gradio_client
        test_with_gradio_client()
    
    # عرض الحلول
    show_solutions()
