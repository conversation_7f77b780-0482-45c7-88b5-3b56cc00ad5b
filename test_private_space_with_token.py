#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Space الخاص باستخدام Token
Test Private Space with Token
"""

import requests
import os
import time
from datetime import datetime

def test_private_space_api():
    """اختبار API للـ Space الخاص"""
    
    print("🔐 اختبار Space الخاص nanami34/ai55")
    print("="*60)
    
    # إعدادات Space الخاص
    TOKEN = "*************************************"
    SPACE_URL = "https://nanami34-ai55.hf.space"  # الرابط المباشر
    API_KEY = "whisper-hf-spaces-2025"
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    
    print(f"🔗 Space URL: {SPACE_URL}")
    print(f"🔑 HF Token: {TOKEN[:10]}...{TOKEN[-5:]}")
    print(f"🎯 API Key: {API_KEY}")
    print(f"📁 ملف الاختبار: {VIDEO_FILE}")
    
    # التحقق من وجود الملف
    if not os.path.exists(VIDEO_FILE):
        print(f"❌ خطأ: الملف غير موجود - {VIDEO_FILE}")
        return
    
    file_size = os.path.getsize(VIDEO_FILE) / (1024 * 1024)
    print(f"📊 حجم الملف: {file_size:.2f} MB")
    print()
    
    # إعداد headers للمصادقة
    headers = {
        'Authorization': f'Bearer {TOKEN}',
        'X-API-Key': API_KEY
    }
    
    # اختبار routes مختلفة
    routes_to_test = [
        ('GET', '/health', 'فحص الصحة'),
        ('GET', '/test', 'اختبار الاتصال'),
        ('GET', '/', 'الصفحة الرئيسية'),
        ('POST', '/api/transcribe', 'تحويل الصوت')
    ]
    
    for method, route, description in routes_to_test:
        print(f"🔍 اختبار {description} ({method} {route})...")
        
        try:
            url = f"{SPACE_URL}{route}"
            
            if method == 'GET':
                response = requests.get(url, headers=headers, timeout=15)
            else:  # POST
                with open(VIDEO_FILE, 'rb') as f:
                    files = {'audio': f}
                    response = requests.post(url, files=files, headers=headers, timeout=120)
            
            print(f"   📡 Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ {description} يعمل!")
                
                # محاولة تحليل JSON
                try:
                    data = response.json()
                    
                    if route == '/health':
                        print(f"      🏥 حالة: {data.get('status', 'غير محدد')}")
                        print(f"      🤖 نموذج: {data.get('model_loaded', 'غير محدد')}")
                        print(f"      📋 Routes: {len(data.get('routes_available', []))}")
                        
                    elif route == '/test':
                        print(f"      🧪 رسالة: {data.get('message', 'غير محدد')}")
                        
                    elif route == '/api/transcribe':
                        if data.get('success'):
                            result_data = data['data']
                            print(f"      🎉 تحويل ناجح!")
                            print(f"      📝 النص: {result_data['text'][:100]}...")
                            print(f"      📊 كلمات: {result_data['word_count']}")
                            print(f"      🌍 لغة: {result_data['language']}")
                            
                            # حفظ النتيجة
                            save_successful_result(result_data, url)
                            show_api_usage_example(SPACE_URL, TOKEN, API_KEY)
                            return True
                        else:
                            print(f"      ❌ خطأ API: {data.get('error')}")
                            
                except ValueError:
                    # ليس JSON، ربما HTML
                    if route == '/':
                        print(f"      📄 صفحة HTML (طبيعي للصفحة الرئيسية)")
                    else:
                        print(f"      📄 استجابة غير JSON: {response.text[:100]}...")
                        
            elif response.status_code == 401:
                print(f"   🔐 يحتاج مصادقة - تحقق من Token")
            elif response.status_code == 404:
                print(f"   ❌ غير موجود - Route غير متاح")
            elif response.status_code == 405:
                print(f"   🔄 Method غير مدعوم")
            else:
                print(f"   ⚠️ حالة غير متوقعة: {response.status_code}")
                print(f"      📄 استجابة: {response.text[:100]}...")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ انتهت مهلة الانتظار")
        except Exception as e:
            print(f"   ❌ خطأ: {str(e)[:60]}...")
        
        print()  # سطر فارغ
    
    return False

def save_successful_result(data, api_url):
    """حفظ النتيجة الناجحة"""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"private_space_success_{timestamp}.txt"
    
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(f"🎉 نجح اختبار Space الخاص!\n")
        f.write(f"{'='*50}\n")
        f.write(f"API URL: {api_url}\n")
        f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"عدد الكلمات: {data['word_count']}\n")
        f.write(f"عدد الأحرف: {data['char_count']}\n")
        f.write(f"اللغة: {data['language']}\n")
        f.write(f"API Key: {data['api_key']}\n")
        f.write(f"\n{'='*50}\n")
        f.write(f"النص المحول:\n")
        f.write(f"{'='*50}\n")
        f.write(data['text'])
    
    print(f"      💾 حفظ النتيجة في: {output_file}")

def show_api_usage_example(space_url, token, api_key):
    """عرض مثال لاستخدام API"""
    
    print(f"\n🔧 مثال لاستخدام API الخاص:")
    print("="*50)
    print("```python")
    print("import requests")
    print("")
    print("# إعدادات Space الخاص")
    print(f"SPACE_URL = '{space_url}'")
    print(f"HF_TOKEN = '{token}'")
    print(f"API_KEY = '{api_key}'")
    print("")
    print("# إعداد headers للمصادقة")
    print("headers = {")
    print("    'Authorization': f'Bearer {HF_TOKEN}',")
    print("    'X-API-Key': API_KEY")
    print("}")
    print("")
    print("# رفع ملف صوتي/فيديو")
    print("files = {'audio': open('your_file.mp3', 'rb')}")
    print("response = requests.post(f'{SPACE_URL}/api/transcribe', files=files, headers=headers)")
    print("")
    print("# معالجة النتيجة")
    print("if response.status_code == 200:")
    print("    result = response.json()")
    print("    if result['success']:")
    print("        data = result['data']")
    print("        print(f'النص: {data[\"text\"]}')") 
    print("        print(f'الكلمات: {data[\"word_count\"]}')") 
    print("        print(f'اللغة: {data[\"language\"]}')") 
    print("    else:")
    print("        print(f'خطأ: {result[\"error\"]}')") 
    print("else:")
    print("    print(f'HTTP Error: {response.status_code}')") 
    print("```")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 اختبار Space الخاص مع Token")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    success = test_private_space_api()
    
    if success:
        print("\n🎉 تم اختبار Space الخاص بنجاح!")
        print("💡 يمكنك الآن استخدام API في تطبيقاتك")
    else:
        print("\n❌ فشل في اختبار Space")
        print("\n💡 اقتراحات:")
        print("   1. تأكد من تحديث ملف app.py في Space")
        print("   2. انتظر إعادة تشغيل Space (1-2 دقيقة)")
        print("   3. تحقق من logs Space للأخطاء")
        print("   4. تأكد من صحة Token")

if __name__ == "__main__":
    main()
