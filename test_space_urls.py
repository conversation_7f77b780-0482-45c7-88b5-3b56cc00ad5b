#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار روابط مختلفة للـ Space
Test different Space URLs
"""

import requests
import time

def test_space_urls():
    """اختبار روابط مختلفة للـ Space"""
    
    print("🔍 اختبار روابط مختلفة للـ Space nanami34/ai55")
    print("="*60)
    
    # روابط محتملة
    possible_urls = [
        "https://nanami34-ai55.hf.space",
        "https://huggingface.co/spaces/nanami34/ai55",
        "https://nanami34-ai55.hf.space",
        "https://hf.space/nanami34/ai55",
        "https://nanami34-ai55.huggingface.co",
    ]
    
    working_urls = []
    
    for url in possible_urls:
        print(f"\n🔗 اختبار: {url}")
        
        try:
            # اختبار الصفحة الرئيسية
            response = requests.get(url, timeout=10)
            print(f"   📡 HTTP Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ الرابط يعمل!")
                working_urls.append(url)
                
                # اختبار endpoints
                test_endpoints(url)
                
            elif response.status_code == 404:
                print(f"   ❌ غير موجود (404)")
            elif response.status_code == 401:
                print(f"   🔐 يحتاج مصادقة (401)")
            else:
                print(f"   ⚠️ حالة غير معروفة: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ انتهت مهلة الانتظار")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ خطأ في الاتصال")
        except Exception as e:
            print(f"   ❌ خطأ: {str(e)[:50]}...")
    
    return working_urls

def test_endpoints(base_url):
    """اختبار endpoints مختلفة"""
    
    endpoints = [
        "/health",
        "/api/transcribe",
        "/",
        "/docs",
        "/api/docs"
    ]
    
    print(f"   🔧 اختبار endpoints:")
    
    for endpoint in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"      ✅ {endpoint} - يعمل")
            elif response.status_code == 404:
                print(f"      ❌ {endpoint} - غير موجود")
            elif response.status_code == 405:
                print(f"      🔄 {endpoint} - يحتاج POST")
            else:
                print(f"      ⚠️ {endpoint} - {response.status_code}")
                
        except:
            print(f"      ❌ {endpoint} - خطأ")

def test_gradio_direct():
    """اختبار الاتصال المباشر بـ Gradio"""
    
    print(f"\n🎯 اختبار الاتصال المباشر بـ Gradio...")
    print("="*50)
    
    try:
        from gradio_client import Client
        
        # جرب أسماء مختلفة
        space_names = [
            "nanami34/ai55",
            "https://nanami34-ai55.hf.space",
            "https://huggingface.co/spaces/nanami34/ai55"
        ]
        
        for space_name in space_names:
            try:
                print(f"🔗 جرب: {space_name}")
                client = Client(space_name)
                print(f"   ✅ نجح الاتصال!")
                
                # جرب الحصول على معلومات API
                try:
                    api_info = client.view_api()
                    print(f"   📋 API متاح")
                    
                    if 'named_endpoints' in api_info:
                        endpoints = list(api_info['named_endpoints'].keys())
                        print(f"   🔧 Endpoints: {endpoints}")
                    
                    return client
                    
                except Exception as e:
                    print(f"   ⚠️ لا يمكن الحصول على API info: {str(e)[:50]}...")
                    return client
                    
            except Exception as e:
                print(f"   ❌ فشل: {str(e)[:50]}...")
                continue
        
        print("❌ فشل في جميع المحاولات")
        return None
        
    except ImportError:
        print("❌ gradio_client غير مثبت")
        return None

def check_space_status():
    """فحص حالة Space"""
    
    print(f"\n📊 فحص حالة Space...")
    print("="*30)
    
    # جرب الوصول لصفحة Space مباشرة
    space_page = "https://huggingface.co/spaces/nanami34/ai55"
    
    try:
        response = requests.get(space_page, timeout=10)
        
        if response.status_code == 200:
            print("✅ صفحة Space متاحة")
            
            # فحص محتوى الصفحة
            content = response.text.lower()
            
            if "running" in content:
                print("🟢 Space يعمل")
            elif "building" in content:
                print("🟡 Space في وضع البناء")
            elif "sleeping" in content:
                print("😴 Space في وضع النوم")
            elif "error" in content:
                print("🔴 Space به خطأ")
            else:
                print("⚪ حالة غير محددة")
                
        else:
            print(f"❌ لا يمكن الوصول لصفحة Space: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص Space: {str(e)}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 اختبار شامل للـ Space الجديد")
    print(f"🕒 الوقت: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # فحص حالة Space
    check_space_status()
    
    # اختبار الروابط
    working_urls = test_space_urls()
    
    if working_urls:
        print(f"\n🎉 تم العثور على روابط تعمل:")
        for url in working_urls:
            print(f"   ✅ {url}")
    else:
        print(f"\n❌ لم يتم العثور على روابط تعمل")
    
    # اختبار Gradio مباشرة
    client = test_gradio_direct()
    
    if client:
        print(f"\n✅ Gradio client يعمل!")
    else:
        print(f"\n❌ Gradio client لا يعمل")
    
    # التوصيات
    print(f"\n💡 التوصيات:")
    if working_urls:
        print(f"   - استخدم أحد الروابط التي تعمل")
        print(f"   - جرب endpoints مختلفة")
    else:
        print(f"   - تحقق من أن Space يعمل في المتصفح")
        print(f"   - قد يكون Space في وضع النوم")
        print(f"   - جرب إعادة تشغيل Space")

if __name__ == "__main__":
    main()
