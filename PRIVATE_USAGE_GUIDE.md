# 🔐 دليل استخدام Space الخاص

## 🚀 الخطوات السريعة:

### 1. إنشاء Hugging Face Token:
1. اذهب إلى: https://huggingface.co/settings/tokens
2. اضغط **"New token"**
3. أدخل اسم: `my-private-api`
4. اختر **"Read"** في الصلاحيات
5. اضغط **"Generate a token"**
6. انسخ الـ token (يبدأ بـ `hf_...`)

### 2. إعداد Token:
1. افتح ملف `config.py`
2. استبدل `YOUR_TOKEN_HERE` بالـ token الخاص بك
3. احفظ الملف

### 3. تشغيل العميل:
```bash
python private_api_client.py
```

## 📁 الملفات المطلوبة:

- `private_api_client.py` - العميل الرئيسي
- `config.py` - إعدادات Token
- `vertical_shorts_exciting_20250715_004149.mp4` - ملف الفيديو للاختبار

## 🎯 ما سيحدث:

1. **الاتصال بـ Space** باستخدام Token
2. **تحويل الفيديو** إلى نص
3. **عرض النتائج** مع الإحصائيات
4. **حفظ النتيجة** في ملف txt

## 📊 النتيجة المتوقعة:

```
🎉 نتائج التحويل:
============================================================
📝 النص المحول:
   [النص المحول من الفيديو]

📊 الإحصائيات:
   📝 عدد الكلمات: XX
   🔤 عدد الأحرف: XXX
   🌍 اللغة: detected
   ⏱️ وقت المعالجة: X.XX ثانية
   📁 حجم الملف: X.XX MB
   🚀 سرعة المعالجة: X.XX MB/ثانية
============================================================
💾 تم حفظ النتيجة في: private_transcription_YYYYMMDD_HHMMSS.txt
```

## 🔧 للاستخدام مع ملفات أخرى:

```python
from private_api_client import PrivateSpaceClient
from config import HUGGING_FACE_TOKEN

# إنشاء العميل
client = PrivateSpaceClient(HUGGING_FACE_TOKEN)

# الاتصال
if client.connect():
    # تحويل ملف
    result = client.transcribe_file("your_audio_file.mp3", "Arabic")
    
    if result:
        print(f"النص: {result['text']}")
        print(f"الكلمات: {result['word_count']}")
```

## 🛡️ الأمان:

- ✅ Token محفوظ في ملف منفصل
- ✅ Space يبقى خاص
- ✅ لا يمكن للآخرين الوصول إليه
- ✅ استخدام آمن ومحدود

## ❓ استكشاف الأخطاء:

### خطأ 401 (Unauthorized):
- تحقق من صحة Token
- تأكد من صلاحية 'Read'
- جرب إنشاء token جديد

### خطأ في الاتصال:
- تحقق من اتصال الإنترنت
- تأكد من أن Space يعمل
- جرب فتح Space في المتصفح

### ملف غير موجود:
- تأكد من وجود الملف في نفس المجلد
- تحقق من اسم الملف
- جرب ملف أصغر للاختبار

## 🎉 جاهز للاستخدام!

بعد إعداد Token، ستتمكن من:
- تحويل أي ملف صوتي/فيديو إلى نص
- الحفاظ على خصوصية Space
- استخدام API بأمان
- الحصول على نتائج دقيقة
