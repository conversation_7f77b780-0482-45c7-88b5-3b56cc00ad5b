#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق Streamlit لتحويل الصوت إلى نص - بديل Gradio
Streamlit app for audio to text conversion - Gradio alternative
"""

import streamlit as st
import whisper
import os
import tempfile
import warnings

# إعدادات الصفحة
st.set_page_config(
    page_title="محول الصوت إلى نص",
    page_icon="🎤",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# إيقاف التحذيرات
warnings.filterwarnings("ignore")

# متغيرات عامة
API_KEY = "whisper-streamlit-2025"

@st.cache_resource
def load_whisper_model():
    """تحميل نموذج Whisper مع التخزين المؤقت"""
    try:
        # محاولة تحميل النموذج المحلي
        if os.path.exists("./models/small.pt"):
            model = whisper.load_model("./models/small.pt")
            st.success("✅ تم تحميل النموذج المحلي")
            return model
        else:
            # تحميل نموذج tiny (سريع وخفيف)
            model = whisper.load_model("tiny")
            st.success("✅ تم تحميل نموذج tiny")
            return model
    except Exception as e:
        st.error(f"خطأ في تحميل النموذج: {e}")
        # محاولة أخيرة مع base
        model = whisper.load_model("base")
        st.success("✅ تم تحميل نموذج base")
        return model

def transcribe_audio(audio_file):
    """تحويل الصوت إلى نص"""
    try:
        # حفظ الملف مؤقتاً
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
            tmp_file.write(audio_file.read())
            tmp_file_path = tmp_file.name
        
        # تحميل النموذج
        model = load_whisper_model()
        
        # تحويل الصوت إلى نص
        with st.spinner('🔄 جاري تحويل الصوت إلى نص...'):
            result = model.transcribe(tmp_file_path)
            text = result["text"].strip()
        
        # حذف الملف المؤقت
        os.unlink(tmp_file_path)
        
        if not text:
            return None, "لم يتم العثور على نص في الملف الصوتي"
        
        # إحصائيات
        word_count = len(text.split())
        char_count = len(text)
        detected_language = result.get("language", "غير محدد")
        
        stats = {
            'text': text,
            'word_count': word_count,
            'char_count': char_count,
            'language': detected_language
        }
        
        return stats, None
        
    except Exception as e:
        return None, f"خطأ في معالجة الملف: {str(e)}"

# واجهة التطبيق
def main():
    # العنوان الرئيسي
    st.markdown("""
    <div style="text-align: center; padding: 2rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; margin-bottom: 2rem;">
        <h1 style="color: white; margin: 0;">🎤 محول الصوت إلى نص</h1>
        <p style="color: white; margin: 0.5rem 0 0 0;">مدعوم بنموذج Whisper - دقة عالية في التحويل</p>
    </div>
    """, unsafe_allow_html=True)
    
    # معلومات API
    with st.expander("🔑 معلومات API", expanded=False):
        st.code(f"API Key: {API_KEY}")
        st.markdown("""
        **للاستخدام البرمجي:**
        ```python
        import requests
        import streamlit as st
        
        # استخدام API (إذا كان متاحاً)
        # هذا التطبيق يستخدم Streamlit وليس API
        ```
        """)
    
    # رفع الملف
    st.markdown("### 📁 ارفع ملف صوتي")
    uploaded_file = st.file_uploader(
        "اختر ملف صوتي",
        type=['mp3', 'wav', 'm4a', 'flac', 'ogg'],
        help="يدعم: MP3, WAV, M4A, FLAC, OGG - حد أقصى: 200MB"
    )
    
    if uploaded_file is not None:
        # عرض معلومات الملف
        file_size = len(uploaded_file.read()) / (1024 * 1024)
        uploaded_file.seek(0)  # إعادة تعيين المؤشر
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("📄 اسم الملف", uploaded_file.name)
        with col2:
            st.metric("📊 حجم الملف", f"{file_size:.2f} MB")
        with col3:
            st.metric("🔧 نوع الملف", uploaded_file.type)
        
        # زر التحويل
        if st.button("🚀 تحويل إلى نص", type="primary", use_container_width=True):
            # تحويل الصوت
            stats, error = transcribe_audio(uploaded_file)
            
            if error:
                st.error(f"❌ {error}")
            else:
                # عرض النتائج
                st.markdown("### 📝 النص المحول")
                st.text_area(
                    "النتيجة:",
                    value=stats['text'],
                    height=200,
                    help="يمكنك نسخ النص من هنا"
                )
                
                # إحصائيات
                st.markdown("### 📊 إحصائيات التحويل")
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("🔤 عدد الأحرف", stats['char_count'])
                with col2:
                    st.metric("📝 عدد الكلمات", stats['word_count'])
                with col3:
                    st.metric("🌍 اللغة المكتشفة", stats['language'])
                with col4:
                    st.metric("🔑 API Key", API_KEY)
                
                # رسالة نجاح
                st.success("✅ تم التحويل بنجاح!")
                
                # إمكانية تحميل النتيجة
                st.download_button(
                    label="📥 تحميل النص كملف",
                    data=stats['text'],
                    file_name=f"transcription_{uploaded_file.name}.txt",
                    mime="text/plain"
                )
    
    # معلومات إضافية
    st.markdown("---")
    st.markdown("""
    ### ℹ️ معلومات التطبيق
    - **النموذج**: OpenAI Whisper
    - **الدقة**: عالية للغة العربية والإنجليزية
    - **الملفات المدعومة**: MP3, WAV, M4A, FLAC, OGG
    - **الحد الأقصى**: 200MB
    - **المعالجة**: محلية وآمنة
    """)

if __name__ == "__main__":
    main()
