#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار API محلياً
Test API locally
"""

import requests
import os
import time
from datetime import datetime

def test_local_api():
    """اختبار API محلياً"""
    
    print("🚀 اختبار API محلياً...")
    print("="*50)
    
    # إعدادات محلية
    LOCAL_URL = "http://127.0.0.1:7860"
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    
    # التحقق من وجود الملف
    if not os.path.exists(VIDEO_FILE):
        print(f"❌ خطأ: الملف غير موجود - {VIDEO_FILE}")
        return
    
    file_size = os.path.getsize(VIDEO_FILE) / (1024 * 1024)
    print(f"📁 ملف الفيديو: {VIDEO_FILE}")
    print(f"📊 حجم الملف: {file_size:.2f} MB")
    print(f"🔗 URL: {LOCAL_URL}")
    print()
    
    try:
        # اختبار صحة التطبيق
        print("🔍 اختبار صحة التطبيق...")
        health_response = requests.get(f"{LOCAL_URL}/health", timeout=5)
        
        if health_response.status_code == 200:
            health_data = health_response.json()
            print("✅ التطبيق يعمل محلياً")
            print(f"   📊 الحالة: {health_data.get('status')}")
            print(f"   🤖 النموذج: {health_data.get('model_loaded')}")
            print(f"   🔑 API Key: {health_data.get('api_key')}")
        else:
            print(f"⚠️ مشكلة في الصحة: {health_response.status_code}")
            
    except Exception as e:
        print(f"❌ التطبيق لا يعمل محلياً: {e}")
        print("💡 تأكد من تشغيل app.py أولاً")
        return
    
    print()
    print("📤 بدء تحويل الفيديو...")
    start_time = time.time()
    
    try:
        with open(VIDEO_FILE, 'rb') as video_file:
            files = {'audio': video_file}
            
            response = requests.post(
                f"{LOCAL_URL}/api/transcribe",
                files=files,
                timeout=300
            )
        
        process_time = time.time() - start_time
        print(f"⏱️ وقت المعالجة: {process_time:.2f} ثانية")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                data = result['data']
                
                print("\n" + "="*60)
                print("🎉 نتائج التحويل:")
                print("="*60)
                print(f"📝 النص المحول:")
                print(f"   {data['text']}")
                print(f"\n📊 الإحصائيات:")
                print(f"   🔤 عدد الأحرف: {data['char_count']}")
                print(f"   📝 عدد الكلمات: {data['word_count']}")
                print(f"   🌍 اللغة المكتشفة: {data['language']}")
                print(f"   ⏱️ وقت المعالجة: {process_time:.2f} ثانية")
                print("="*60)
                
                # حفظ النتيجة في ملف
                with open("transcription_result.txt", "w", encoding="utf-8") as f:
                    f.write(f"نتائج تحويل الفيديو إلى نص\n")
                    f.write(f"الملف: {VIDEO_FILE}\n")
                    f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"وقت المعالجة: {process_time:.2f} ثانية\n")
                    f.write(f"اللغة: {data['language']}\n")
                    f.write(f"عدد الكلمات: {data['word_count']}\n")
                    f.write(f"عدد الأحرف: {data['char_count']}\n")
                    f.write(f"\nالنص المحول:\n{data['text']}")
                
                print(f"💾 تم حفظ النتيجة في: transcription_result.txt")
                
            else:
                print(f"❌ خطأ من API: {result.get('error')}")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            print(f"📄 الاستجابة: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ خطأ في المعالجة: {e}")

def show_hf_instructions():
    """عرض تعليمات Hugging Face"""
    print("\n" + "="*60)
    print("📋 تعليمات اختبار Hugging Face Spaces:")
    print("="*60)
    print("1. اذهب إلى Hugging Face Spaces الخاص بك")
    print("2. انسخ الرابط الصحيح (مثل: https://username-spacename.hf.space)")
    print("3. استبدل الرابط في test_api_video.py")
    print("4. شغل السكريبت مرة أخرى")
    print()
    print("🔗 تنسيق الرابط الصحيح:")
    print("   https://[username]-[spacename].hf.space")
    print("   أو")
    print("   https://huggingface.co/spaces/[username]/[spacename]")
    print()
    print("💡 تأكد من:")
    print("   - Space مفعل وليس في وضع النوم")
    print("   - Space عام (Public) وليس خاص")
    print("   - التطبيق يعمل بدون أخطاء")

if __name__ == "__main__":
    print("🎬 اختبار تحويل الفيديو إلى نص")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    test_local_api()
    show_hf_instructions()
