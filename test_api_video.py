#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار API تحويل الفيديو إلى نص
Test API for video to text conversion
"""

import requests
import os
import time
from datetime import datetime

def test_api_transcription(video_file_path, api_url):
    """
    اختبار API لتحويل الفيديو إلى نص
    Test API for video to text conversion
    """
    
    print("🎬 بدء اختبار تحويل الفيديو إلى نص...")
    print(f"📁 ملف الفيديو: {video_file_path}")
    print(f"🔗 API URL: {api_url}")
    print("-" * 50)
    
    # التحقق من وجود الملف
    if not os.path.exists(video_file_path):
        print(f"❌ خطأ: الملف غير موجود - {video_file_path}")
        return None
    
    # معلومات الملف
    file_size = os.path.getsize(video_file_path) / (1024 * 1024)  # MB
    print(f"📊 حجم الملف: {file_size:.2f} MB")
    
    try:
        # إعداد الطلب
        print("📤 رفع الملف إلى API...")
        start_time = time.time()
        
        with open(video_file_path, 'rb') as video_file:
            files = {'audio': video_file}  # API يتوقع 'audio' حتى للفيديو
            
            # إرسال الطلب
            response = requests.post(
                f"{api_url}/api/transcribe",
                files=files,
                timeout=300  # 5 دقائق timeout
            )
        
        upload_time = time.time() - start_time
        print(f"⏱️ وقت الرفع والمعالجة: {upload_time:.2f} ثانية")
        
        # التحقق من الاستجابة
        print(f"📡 HTTP Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ تم استلام الاستجابة بنجاح")
                
                if result.get('success'):
                    data = result['data']
                    
                    print("\n" + "="*60)
                    print("🎉 نتائج التحويل:")
                    print("="*60)
                    print(f"📝 النص المحول:")
                    print(f"   {data['text']}")
                    print(f"\n📊 الإحصائيات:")
                    print(f"   🔤 عدد الأحرف: {data['char_count']}")
                    print(f"   📝 عدد الكلمات: {data['word_count']}")
                    print(f"   🌍 اللغة المكتشفة: {data['language']}")
                    print(f"   🔑 API Key: {data['api_key']}")
                    print(f"   ⏱️ وقت المعالجة: {upload_time:.2f} ثانية")
                    print("="*60)
                    
                    return data
                else:
                    print(f"❌ خطأ من API: {result.get('error', 'خطأ غير محدد')}")
                    return None
                    
            except ValueError as e:
                print(f"❌ خطأ في تحليل JSON: {e}")
                print(f"📄 محتوى الاستجابة: {response.text[:500]}...")
                return None
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            print(f"📄 محتوى الخطأ: {response.text[:500]}...")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الانتظار (Timeout)")
        print("💡 الملف قد يكون كبير جداً أو الخادم مشغول")
        return None
        
    except requests.exceptions.ConnectionError:
        print("❌ خطأ في الاتصال")
        print("💡 تحقق من رابط API أو الاتصال بالإنترنت")
        return None
        
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {str(e)}")
        return None

def test_health_endpoint(api_url):
    """اختبار endpoint صحة التطبيق"""
    try:
        print("🔍 اختبار صحة التطبيق...")
        response = requests.get(f"{api_url}/health", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            print("✅ التطبيق يعمل بصحة جيدة")
            print(f"   📊 الحالة: {health_data.get('status', 'غير محدد')}")
            print(f"   🤖 النموذج محمل: {health_data.get('model_loaded', 'غير محدد')}")
            print(f"   🔑 API Key: {health_data.get('api_key', 'غير محدد')}")
            print(f"   🏗️ المنصة: {health_data.get('platform', 'غير محدد')}")
            return True
        else:
            print(f"⚠️ مشكلة في صحة التطبيق: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصحة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار API تحويل الفيديو إلى نص")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # إعدادات API
    # جرب عدة روابط محتملة
    possible_urls = [
        "https://sidivall-ai-extact.hf.space",
        "https://huggingface.co/spaces/sidivall/ai_extact",
        "https://sidivall-ai-extract.hf.space",
        "https://sidivall-audio-to-text.hf.space"
    ]

    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    
    print(f"📁 ملف الفيديو: {VIDEO_FILE}")
    print()

    # اختبار عدة روابط محتملة
    working_url = None
    for url in possible_urls:
        print(f"🔍 اختبار الرابط: {url}")
        if test_health_endpoint(url):
            working_url = url
            print(f"✅ تم العثور على رابط يعمل: {url}")
            break
        else:
            print(f"❌ الرابط لا يعمل: {url}")

    if working_url:
        print(f"\n🎯 استخدام الرابط: {working_url}")
        # اختبار تحويل الفيديو
        result = test_api_transcription(VIDEO_FILE, working_url)

        if result:
            print("\n🎉 تم الاختبار بنجاح!")
            print("💡 يمكنك الآن استخدام API في تطبيقاتك")
            print(f"🔗 الرابط الصحيح: {working_url}")
        else:
            print("\n❌ فشل الاختبار")
            print("💡 راجع الأخطاء أعلاه وحاول مرة أخرى")
    else:
        print("\n❌ لم يتم العثور على رابط يعمل")
        print("💡 تحقق من:")
        print("   - اسم Hugging Face Space")
        print("   - حالة التطبيق (قد يكون في وضع النوم)")
        print("   - إعدادات الخصوصية (Public/Private)")

        # اقتراح الرابط اليدوي
        print(f"\n🔗 جرب الروابط التالية يدوياً:")
        for url in possible_urls:
            print(f"   {url}")

if __name__ == "__main__":
    main()
