# 🔐 حلول للتعامل مع Hugging Face Space الخاص

## 🎯 المشكلة:
Space الخاص بك محدد كـ **Private** مما يمنع الوصول إلى API من الخارج.

## ✅ الحلول المتاحة:

### 1. 🌍 جعل Space عام (الحل الأسهل)

**الخطوات:**
1. اذهب إلى https://huggingface.co/spaces/sidivall/ai_extact
2. اضغط على **Settings** (الإعدادات)
3. في قسم **Visibility**، غير من **Private** إلى **Public**
4. احفظ التغييرات

**المميزات:**
- ✅ API يصبح متاح للجميع
- ✅ يمكن اختباره من أي مكان
- ✅ لا حاجة لمصادقة

**العيوب:**
- ⚠️ أي شخص يمكنه استخدام التطبيق
- ⚠️ قد يستهلك موارد أكثر

### 2. 🔑 استخدام Hugging Face Token (للاستخدام الخاص)

**إنشاء Token:**
1. اذهب إلى https://huggingface.co/settings/tokens
2. اضغط **New token**
3. اختر **Read** permissions
4. انسخ الـ token

**استخدام Token في API:**
```python
import requests

# استخدام Token للوصول للـ Space الخاص
headers = {
    'Authorization': 'Bearer YOUR_HF_TOKEN_HERE'
}

files = {'audio': open('audio.mp3', 'rb')}
response = requests.post(
    'https://sidivall-ai-extact.hf.space/api/transcribe',
    files=files,
    headers=headers
)
```

### 3. 📱 استخدام gradio_client (الأفضل للـ Private)

**التثبيت:**
```bash
pip install gradio_client
```

**الاستخدام:**
```python
from gradio_client import Client

# الاتصال بـ Space الخاص
client = Client("sidivall/ai_extact", hf_token="YOUR_HF_TOKEN")

# رفع ملف وتحويله
result = client.predict(
    "path/to/audio.mp3",  # ملف الصوت
    "Arabic",             # اللغة
    api_name="/predict"
)

print(result)
```

### 4. 🔄 إنشاء Space عام منفصل

**إنشاء نسخة عامة:**
1. انسخ جميع ملفات Space الحالي
2. أنشئ Space جديد عام
3. ارفع الملفات
4. استخدم النسخة العامة للـ API

## 🎯 التوصية:

### للاستخدام الشخصي فقط:
- استخدم **الحل رقم 3** (gradio_client)
- يحافظ على الخصوصية
- سهل الاستخدام

### للمشاركة مع الآخرين:
- استخدم **الحل رقم 1** (جعل Space عام)
- أسهل للاختبار والاستخدام

## 🔧 مثال كامل للاستخدام الخاص:

```python
from gradio_client import Client
import os

def transcribe_with_private_space(audio_file, hf_token):
    """تحويل صوت باستخدام Space خاص"""
    try:
        # الاتصال بـ Space الخاص
        client = Client("sidivall/ai_extact", hf_token=hf_token)
        
        # تحويل الملف
        result = client.predict(
            audio_file,
            "Arabic",  # أو "تلقائي"
            api_name="/predict"
        )
        
        return result
        
    except Exception as e:
        print(f"خطأ: {e}")
        return None

# الاستخدام
hf_token = "YOUR_HF_TOKEN_HERE"
audio_file = "vertical_shorts_exciting_20250715_004149.mp4"

result = transcribe_with_private_space(audio_file, hf_token)
if result:
    print(f"النص: {result[0]}")  # النص المحول
    print(f"المعلومات: {result[1]}")  # معلومات إضافية
```

## 🚀 الخطوات التالية:

1. **اختر الحل المناسب** حسب احتياجاتك
2. **جرب الكود** المناسب
3. **اختبر التحويل** مع ملف الفيديو

أي حل تفضل أن نجربه؟
