#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Space الخاص باستخدام Hugging Face Token
Test Private Space with Hugging Face Token
"""

import os
import time
from datetime import datetime

def test_with_token():
    """اختبار باستخدام Token"""
    
    print("🔑 اختبار Space الخاص باستخدام Token")
    print("="*60)
    
    # ضع الـ Token الخاص بك هنا
    HF_TOKEN = "YOUR_HUGGING_FACE_TOKEN_HERE"  # استبدل بالـ token الخاص بك
    
    if HF_TOKEN == "YOUR_HUGGING_FACE_TOKEN_HERE":
        print("❌ يرجى إضافة Hugging Face Token:")
        print("1. اذهب إلى https://huggingface.co/settings/tokens")
        print("2. اضغط 'New token'")
        print("3. اختر 'Read' permissions")
        print("4. انسخ الـ token وضعه في المتغير HF_TOKEN")
        return
    
    try:
        from gradio_client import Client
        print("✅ gradio_client متوفر")
    except ImportError:
        print("❌ gradio_client غير مثبت")
        print("💡 قم بتثبيته: pip install gradio_client")
        return
    
    # إعدادات
    SPACE_NAME = "sidivall/ai_extact"
    VIDEO_FILE = "vertical_shorts_exciting_20250715_004149.mp4"
    
    # التحقق من وجود الملف
    if not os.path.exists(VIDEO_FILE):
        print(f"❌ خطأ: الملف غير موجود - {VIDEO_FILE}")
        return
    
    file_size = os.path.getsize(VIDEO_FILE) / (1024 * 1024)
    print(f"📁 ملف الفيديو: {VIDEO_FILE}")
    print(f"📊 حجم الملف: {file_size:.2f} MB")
    print(f"🏠 Space: {SPACE_NAME}")
    print(f"🔑 Token: {HF_TOKEN[:10]}...{HF_TOKEN[-5:] if len(HF_TOKEN) > 15 else 'قصير'}")
    print()
    
    try:
        print("🔗 الاتصال بـ Space باستخدام Token...")
        start_time = time.time()
        
        # الاتصال باستخدام Token
        client = Client(SPACE_NAME, hf_token=HF_TOKEN)
        
        connect_time = time.time() - start_time
        print(f"✅ تم الاتصال بنجاح ({connect_time:.2f} ثانية)")
        
        # الحصول على معلومات API
        print("📋 معلومات API:")
        try:
            api_info = client.view_api()
            print(f"   📡 API متاح: نعم")
            
            # عرض endpoints المتاحة
            if 'named_endpoints' in api_info:
                endpoints = list(api_info['named_endpoints'].keys())
                print(f"   🔧 Endpoints متاحة: {endpoints}")
            
        except Exception as e:
            print(f"   ⚠️ لا يمكن الحصول على معلومات API: {str(e)[:50]}...")
        
        print()
        print("🎯 بدء تحويل الفيديو...")
        transcribe_start = time.time()
        
        # جرب endpoints مختلفة
        possible_calls = [
            # للـ Gradio Interface
            lambda: client.predict(VIDEO_FILE, "Arabic", api_name="/predict"),
            lambda: client.predict(VIDEO_FILE, "تلقائي", api_name="/predict"),
            
            # للـ Flask API (إذا كان متاح)
            lambda: client.predict(VIDEO_FILE, api_name="/transcribe_audio"),
            lambda: client.predict(VIDEO_FILE, "Arabic", api_name="/transcribe_audio"),
        ]
        
        result = None
        successful_call = None
        
        for i, call_func in enumerate(possible_calls):
            try:
                print(f"   🔍 جرب الطريقة {i+1}...")
                result = call_func()
                successful_call = i + 1
                print(f"   ✅ نجح مع الطريقة {i+1}")
                break
            except Exception as e:
                print(f"   ❌ فشل الطريقة {i+1}: {str(e)[:80]}...")
                continue
        
        if result is None:
            print("❌ فشل في جميع الطرق")
            print("💡 جرب:")
            print("   - التأكد من أن Space يعمل")
            print("   - فتح Space في المتصفح واختبار الواجهة")
            return
        
        transcribe_time = time.time() - transcribe_start
        
        # عرض النتائج
        print("\n" + "="*60)
        print("🎉 نتائج التحويل:")
        print("="*60)
        print(f"✅ نجح باستخدام الطريقة {successful_call}")
        
        # تحليل النتيجة
        if isinstance(result, (list, tuple)):
            print(f"📊 نوع النتيجة: قائمة/مجموعة ({len(result)} عناصر)")
            
            for i, item in enumerate(result):
                if item:  # إذا كان العنصر ليس فارغ
                    print(f"📝 العنصر {i+1}: {str(item)[:200]}{'...' if len(str(item)) > 200 else ''}")
            
            # استخراج النص (عادة العنصر الأول)
            if len(result) > 0 and result[0]:
                text = str(result[0])
                word_count = len(text.split()) if text else 0
                char_count = len(text) if text else 0
                
                print(f"\n📊 الإحصائيات:")
                print(f"   📝 عدد الكلمات: {word_count}")
                print(f"   🔤 عدد الأحرف: {char_count}")
                print(f"   ⏱️ وقت التحويل: {transcribe_time:.2f} ثانية")
                
        else:
            print(f"📄 النتيجة المباشرة: {str(result)[:300]}{'...' if len(str(result)) > 300 else ''}")
        
        print("="*60)
        
        # حفظ النتيجة
        output_file = f"private_token_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(f"نتائج تحويل Space الخاص باستخدام Token\n")
            f.write(f"{'='*50}\n")
            f.write(f"Space: {SPACE_NAME}\n")
            f.write(f"الملف: {VIDEO_FILE}\n")
            f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"وقت الاتصال: {connect_time:.2f} ثانية\n")
            f.write(f"وقت التحويل: {transcribe_time:.2f} ثانية\n")
            f.write(f"الطريقة الناجحة: {successful_call}\n")
            f.write(f"\nالنتيجة الكاملة:\n{result}")
        
        print(f"💾 تم حفظ النتيجة في: {output_file}")
        
        return result
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ خطأ في الاتصال: {error_msg}")
        
        if "401" in error_msg or "unauthorized" in error_msg.lower():
            print("\n🔑 مشكلة في Token:")
            print("   - تأكد من صحة Token")
            print("   - تأكد من أن Token له صلاحية 'Read'")
            print("   - جرب إنشاء token جديد")
        
        return None

def show_token_instructions():
    """عرض تعليمات إنشاء Token"""
    
    print("\n" + "="*60)
    print("🔑 كيفية إنشاء Hugging Face Token:")
    print("="*60)
    print("1. اذهب إلى https://huggingface.co/settings/tokens")
    print("2. اضغط 'New token'")
    print("3. أدخل اسم للـ token (مثل: 'my-api-token')")
    print("4. اختر 'Read' في الصلاحيات")
    print("5. اضغط 'Generate a token'")
    print("6. انسخ الـ token (يبدأ بـ hf_...)")
    print("7. ضعه في المتغير HF_TOKEN في هذا الملف")
    print()
    print("⚠️ احتفظ بالـ token آمن ولا تشاركه مع أحد!")

if __name__ == "__main__":
    print("🚀 اختبار Space الخاص باستخدام Token")
    print(f"🕒 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    result = test_with_token()
    
    if not result:
        show_token_instructions()
    else:
        print("\n🎉 تم الاختبار بنجاح!")
        print("💡 يمكنك الآن استخدام نفس الطريقة في تطبيقاتك")
